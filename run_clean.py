#!/usr/bin/env python3
"""
Clean runner for Accent Recognition API
Suppresses verbose output and shows only essential information
"""

import subprocess
import sys
import os
import warnings
from pathlib import Path
import torch
import threading
import time

def show_download_progress():
    """Show download progress animation"""
    chars = "⠋⠙⠹⠸⠼⠴⠦⠧⠇⠏"
    idx = 0
    start_time = time.time()

    while True:
        elapsed = int(time.time() - start_time)
        mins, secs = divmod(elapsed, 60)
        print(f"\r🔄 Downloading model... {chars[idx % len(chars)]} [{mins:02d}:{secs:02d}]", end="", flush=True)
        idx += 1
        time.sleep(0.1)

def get_gpu_info():
    """Get GPU information for display"""
    try:
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            current_device = torch.cuda.current_device()
            gpu_name = torch.cuda.get_device_name(current_device)
            cuda_version = torch.version.cuda
            pytorch_version = torch.__version__

            return {
                "available": True,
                "count": gpu_count,
                "current_device": current_device,
                "name": gpu_name,
                "cuda_version": cuda_version,
                "pytorch_version": pytorch_version
            }
        else:
            return {
                "available": False,
                "pytorch_version": torch.__version__
            }
    except Exception as e:
        return {
            "available": False,
            "error": str(e),
            "pytorch_version": getattr(torch, '__version__', 'unknown')
        }

def setup_clean_environment():
    """Setup environment to suppress verbose output"""
    # Suppress Python warnings
    warnings.filterwarnings("ignore")

    # Set environment variables to reduce verbosity
    os.environ["PYTHONWARNINGS"] = "ignore"
    os.environ["TRANSFORMERS_VERBOSITY"] = "error"
    os.environ["SPEECHBRAIN_CACHE"] = "pretrained_models"
    os.environ["TORCHAUDIO_BACKEND"] = "soundfile"

    # Additional environment variables to suppress output
    os.environ["TOKENIZERS_PARALLELISM"] = "false"
    os.environ["HF_HUB_DISABLE_PROGRESS_BARS"] = "1"
    os.environ["TRANSFORMERS_NO_ADVISORY_WARNINGS"] = "1"

def check_virtual_environment():
    """Check if virtual environment is activated"""
    # Check if we're in a virtual environment
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        return True

    # Check if venv directory exists
    venv_path = Path("venv")
    if not venv_path.exists():
        return False

    return True

def run_clean_server():
    """Run uvicorn with clean output"""
    print("Accent Recognition API")
    print("=" * 40)

    # Check virtual environment
    if not check_virtual_environment():
        print("❌ Virtual environment not found or not activated!")
        print("\nPlease activate virtual environment first:")
        print("   Windows: venv\\Scripts\\activate")
        print("   Linux/macOS: source venv/bin/activate")
        print("\nThen run this script again.")
        return False

    print("✅ Virtual environment detected")

    # Display GPU information
    gpu_info = get_gpu_info()
    if gpu_info["available"]:
        print(f"🚀 GPU Acceleration: ENABLED")
        print(f"   GPU: {gpu_info['name']}")
        print(f"   CUDA Version: {gpu_info['cuda_version']}")
        print(f"   PyTorch Version: {gpu_info['pytorch_version']}")
        if gpu_info["count"] > 1:
            print(f"   Available GPUs: {gpu_info['count']} (using GPU {gpu_info['current_device']})")
    else:
        print(f"💻 GPU Acceleration: DISABLED (CPU mode)")
        print(f"   PyTorch Version: {gpu_info['pytorch_version']}")
        if "error" in gpu_info:
            print(f"   Note: {gpu_info['error']}")
        else:
            print(f"   Note: CUDA not available or not installed")

    print("Starting server...")

    # Determine python path based on OS
    if os.name == 'nt':  # Windows
        python_path = Path("venv/Scripts/python.exe")
        if not python_path.exists():
            python_path = "python"
    else:  # Linux/macOS
        python_path = Path("venv/bin/python")
        if not python_path.exists():
            python_path = "python"

    try:
        # Setup clean environment
        setup_clean_environment()

        print("API will be available at: http://localhost:8000")
        print("API documentation at: http://localhost:8000/docs")
        print("Health check at: http://localhost:8000/health")
        print("\nPress Ctrl+C to stop the server\n")

        # Start progress monitor in background thread
        progress_thread = None

        # Start the server with clean logging
        # The request/response logging is handled by middleware in main.py
        process = subprocess.Popen([
            str(python_path), "-m", "uvicorn", "main:app",
            "--host", "0.0.0.0",
            "--port", "8000",
            "--log-level", "warning",  # Only show warnings and errors from uvicorn
            "--no-access-log"  # Disable uvicorn access logs (we use custom middleware)
        ], stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True, bufsize=1)

        model_loading_detected = False

        # Monitor output for model loading
        for line in iter(process.stdout.readline, ''):
            line = line.strip()
            if line:
                # Check if model loading started
                if "Loading accent recognition model" in line or "WARNING: Xet Storage" in line:
                    if not model_loading_detected:
                        model_loading_detected = True
                        print("🔄 Model download/loading started...")
                        # Start progress animation
                        progress_thread = threading.Thread(target=show_download_progress, daemon=True)
                        progress_thread.start()

                # Check if model loading finished
                elif ("Model loaded and optimized" in line or
                      "Model warmup successful" in line or
                      "Final device mode" in line):
                    if progress_thread and progress_thread.is_alive():
                        # Stop progress animation
                        print("\r✅ Model loading completed!                    ")
                        model_loading_detected = False

                # Always print the line (this includes our custom server output)
                if not ("⠋" in line or "⠙" in line or "⠹" in line):  # Skip progress chars
                    print(line)

        # Wait for process to complete
        process.wait()
        
    except KeyboardInterrupt:
        print("\nServer stopped by user")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Server failed to start: {e}")
        return False
    except FileNotFoundError:
        print("Python or uvicorn not found. Make sure virtual environment is activated.")
        print("   Try: venv\\Scripts\\activate (Windows) or source venv/bin/activate (Linux/macOS)")
        return False
    
    return True

def main():
    """Main function"""
    if not run_clean_server():
        sys.exit(1)

if __name__ == "__main__":
    main()
